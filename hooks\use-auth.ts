"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { createClient } from "@/lib/supabase/client";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import type {
  Profile,
  AppRole,
  ProviderRoleType,
  UserRoleData,
} from "@/types";
import type { User } from "@supabase/supabase-js";

/* ------------------------------------------------------------------ */
/* Query Keys - Following official patterns                          */
/* ------------------------------------------------------------------ */

export const authKeys = {
  all: ['auth'] as const,
  user: () => [...authKeys.all, 'user'] as const,
  profile: (userId?: string) => [...authKeys.all, 'profile', userId] as const,
  userRole: (userId?: string) => [...authKeys.all, 'userRole', userId] as const,
} as const;

/* ------------------------------------------------------------------ */
/* Helpers & constants                                                */
/* ------------------------------------------------------------------ */

const STALE_5_MIN = 5 * 60 * 1000;
const GC_10_MIN = 10 * 60 * 1000;

function getSupabase() {
  return createClient();
}

/* ------------------------------------------------------------------ */
/* Core Auth Hooks - Following Official Supabase Patterns           */
/* ------------------------------------------------------------------ */

/**
 * Get the current authenticated user
 * Uses official Supabase auth.getUser() method
 * Following official documentation: https://supabase.com/docs/guides/auth/server-side/nextjs
 */
export function useUser() {
  const supabase = getSupabase();

  return useQuery({
    queryKey: authKeys.user(),
    queryFn: async (): Promise<User | null> => {
      const { data, error } = await supabase.auth.getUser();
      if (error) {
        // Don't throw on auth errors, just return null
        if (error.message?.includes("Invalid JWT") || error.message?.includes("JWT expired")) {
          return null;
        }
        throw error;
      }
      return data.user;
    },
    staleTime: STALE_5_MIN,
    gcTime: GC_10_MIN,
    retry: (failureCount, error: any) => {
      // Don't retry on auth errors
      if (error?.message?.includes("Invalid JWT") || error?.message?.includes("JWT expired")) {
        return false;
      }
      return failureCount < 2;
    },
  });
}

/**
 * Get user profile data
 * Automatically uses current user's ID if no userId provided
 */
export function useProfile(userId?: string) {
  const supabase = getSupabase();
  const { data: user } = useUser();
  const targetUserId = userId || user?.id;

  return useQuery({
    queryKey: authKeys.profile(targetUserId),
    queryFn: async (): Promise<Profile | null> => {
      if (!targetUserId) return null;

      const { data, error } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", targetUserId)
        .single();

      if (error) {
        if (error.code === "PGRST116") return null; // No rows found
        throw error;
      }
      return data;
    },
    enabled: !!targetUserId,
    staleTime: STALE_5_MIN,
    gcTime: GC_10_MIN,
  });
}

/**
 * Get user role and provider role data
 * Uses simplified role structure: admin, user, catering_provider
 * Fetches directly from database instead of JWT decoding
 */
export function useUserRole(userId?: string) {
  const supabase = getSupabase();
  const { data: user } = useUser();
  const targetUserId = userId || user?.id;

  return useQuery({
    queryKey: authKeys.userRole(targetUserId),
    queryFn: async (): Promise<UserRoleData | null> => {
      if (!targetUserId) return null;

      const { data, error } = await supabase
        .from("user_roles")
        .select("role, provider_role")
        .eq("user_id", targetUserId)
        .single();

      if (error) {
        if (error.code === "PGRST116") return null; // No rows found
        throw error;
      }

      return {
        role: data.role as AppRole,
        provider_role: data.provider_role as ProviderRoleType | null,
      };
    },
    enabled: !!targetUserId,
    staleTime: STALE_5_MIN,
    gcTime: GC_10_MIN,
  });
}

/* ------------------------------------------------------------------ */
/* Authentication Mutations                                           */
/* ------------------------------------------------------------------ */

/**
 * Sign in with email and password
 * Uses official Supabase auth.signInWithPassword method
 */
export function useSignIn() {
  const supabase = getSupabase();
  const queryClient = useQueryClient();
  const router = useRouter();

  return useMutation({
    mutationFn: async ({ email, password }: { email: string; password: string }) => {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      // Invalidate auth queries to refetch user data
      queryClient.invalidateQueries({ queryKey: authKeys.all });
      toast.success("Signed in successfully");
      router.push("/dashboard");
      router.refresh();
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

/**
 * Sign out the current user
 * Uses official Supabase auth.signOut method
 */
export function useSignOut() {
  const supabase = getSupabase();
  const queryClient = useQueryClient();
  const router = useRouter();

  return useMutation({
    mutationFn: async () => {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
    },
    onSuccess: () => {
      // Clear all auth-related caches
      queryClient.removeQueries({ queryKey: authKeys.all });
      router.push("/login");
      router.refresh();
      toast.success("Signed out successfully");
    },
    onError: (error: Error) => {
      toast.error(`Error signing out: ${error.message}`);
    },
  });
}

/* ------------------------------------------------------------------ */
/* OAuth Authentication Mutations                                     */
/* ------------------------------------------------------------------ */

/**
 * Sign in with Google OAuth
 * Uses official Supabase OAuth flow
 */
export function useSignInWithGoogle() {
  const supabase = getSupabase();

  return useMutation({
    mutationFn: async () => {
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: "google",
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
          scopes: "profile email",
        },
      });

      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      if (data.url) {
        window.location.href = data.url;
      }
    },
    onError: (error: Error) => {
      toast.error(`Google sign-in failed: ${error.message}`);
    },
  });
}

/**
 * Sign in with Facebook OAuth
 * Uses official Supabase OAuth flow
 */
export function useSignInWithFacebook() {
  const supabase = getSupabase();

  return useMutation({
    mutationFn: async () => {
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: "facebook",
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
        },
      });

      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      if (data.url) {
        window.location.href = data.url;
      }
    },
    onError: (error: Error) => {
      toast.error(`Facebook sign-in failed: ${error.message}`);
    },
  });
}

/**
 * Sign up with email and password
 * Uses official Supabase auth.signUp method
 */
export function useSignUp() {
  const supabase = getSupabase();

  return useMutation({
    mutationFn: async ({ email, password, fullName }: { email: string; password: string; fullName: string }) => {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
          },
        },
      });

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      toast.success("Check your email for the confirmation link.");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

/* ------------------------------------------------------------------ */
/* Convenience Hooks for Role Checking                               */
/* ------------------------------------------------------------------ */

/**
 * Check if current user is an admin
 */
export function useIsAdmin() {
  const { data: userRole, isLoading, error } = useUserRole();
  return {
    isAdmin: userRole?.role === "admin",
    isLoading,
    error,
  };
}

/**
 * Check if current user is a regular user
 */
export function useIsUser() {
  const { data: userRole, isLoading, error } = useUserRole();
  return {
    isUser: userRole?.role === "user",
    isLoading,
    error,
  };
}

/**
 * Check if current user is a catering provider
 */
export function useIsProvider() {
  const { data: userRole, isLoading, error } = useUserRole();
  return {
    isProvider: userRole?.role === "catering_provider",
    isLoading,
    error,
  };
}

/**
 * Check if current user is a provider owner
 */
export function useIsProviderOwner() {
  const { data: userRole, isLoading, error } = useUserRole();
  return {
    isProviderOwner: userRole?.role === "catering_provider" && userRole?.provider_role === "owner",
    isLoading,
    error,
  };
}

/**
 * Check if current user is a provider staff
 */
export function useIsProviderStaff() {
  const { data: userRole, isLoading, error } = useUserRole();
  return {
    isProviderStaff: userRole?.role === "catering_provider" && userRole?.provider_role === "staff",
    isLoading,
    error,
  };
}

/* ------------------------------------------------------------------ */
/* Composite Auth State Hook                                          */
/* ------------------------------------------------------------------ */

/**
 * Comprehensive authentication state hook
 * Provides all auth-related data and states in one place
 * Following official Supabase patterns without complex JWT decoding
 */
export function useAuthState() {
  const { data: user, isLoading: userLoading, error: userError } = useUser();
  const { data: profile, isLoading: profileLoading } = useProfile();
  const { data: userRole, isLoading: roleLoading } = useUserRole();

  const isLoading = userLoading || profileLoading || roleLoading;
  const isAuthenticated = !!user && !userError;
  const isAdmin = userRole?.role === "admin";
  const isProvider = userRole?.role === "catering_provider";
  const isProviderOwner = isProvider && userRole?.provider_role === "owner";
  const isProviderStaff = isProvider && userRole?.provider_role === "staff";

  return {
    user,
    profile,
    userRole,
    isLoading,
    isAuthenticated,
    isAdmin,
    isProvider,
    isProviderOwner,
    isProviderStaff,
    error: userError,
  };
}

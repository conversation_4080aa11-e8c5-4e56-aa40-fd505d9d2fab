'use server'

import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'
import { createClient } from '@/lib/supabase/server'
import type { AppRole, ProviderRoleType } from '@/types'

/* ================================================================== */
/* AUTHENTICATION ACTIONS - REBUILT FROM SCRATCH                    */
/* Following official Supabase patterns with proper error handling   */
/* ================================================================== */

/**
 * Sign in with email and password
 * Follows official Supabase authentication patterns
 */
export async function login(formData: FormData) {
  try {
    const supabase = await createClient()

    const email = formData.get('email') as string
    const password = formData.get('password') as string

    if (!email || !password) {
      return { error: 'Email and password are required' }
    }

    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })

    if (error) {
      return { error: error.message }
    }

    revalidatePath('/', 'layout')
    redirect('/dashboard')
  } catch (error) {
    console.error('Login error:', error)
    return { error: 'An unexpected error occurred during login' }
  }
}

/**
 * Sign in with Google OAuth
 * Follows official Supabase OAuth patterns
 */
export async function signInWithGoogle() {
  try {
    const supabase = await createClient()

    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback`,
        scopes: 'profile email',
        queryParams: {
          access_type: 'offline',
          prompt: 'consent',
        },
      },
    })

    if (error) {
      return { error: error.message }
    }

    return { url: data.url }
  } catch (error) {
    console.error('Google sign-in error:', error)
    return { error: 'An unexpected error occurred during Google sign-in' }
  }
}

/**
 * Sign in with Facebook OAuth
 * Follows official Supabase OAuth patterns
 */
export async function signInWithFacebook() {
  try {
    const supabase = await createClient()

    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'facebook',
      options: {
        redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback`,
        scopes: 'email',
      },
    })

    if (error) {
      return { error: error.message }
    }

    return { url: data.url }
  } catch (error) {
    console.error('Facebook sign-in error:', error)
    return { error: 'An unexpected error occurred during Facebook sign-in' }
  }
}

/**
 * Sign up with email and password
 * Assigns admin role by default as requested
 */
export async function signup(formData: FormData) {
  try {
    const supabase = await createClient()

    const email = formData.get('email') as string
    const password = formData.get('password') as string
    const fullName = formData.get('full_name') as string

    if (!email || !password || !fullName) {
      return { error: 'Email, password, and full name are required' }
    }

    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: fullName,
        },
      },
    })

    if (error) {
      return { error: error.message }
    }

    return { success: 'Check your email for the confirmation link.' }
  } catch (error) {
    console.error('Signup error:', error)
    return { error: 'An unexpected error occurred during signup' }
  }
}

/**
 * Sign out the current user
 * Clears session and redirects to home page
 */
export async function signout() {
  try {
    const supabase = await createClient()
    await supabase.auth.signOut()
    revalidatePath('/', 'layout')
    redirect('/')
  } catch (error) {
    console.error('Signout error:', error)
    // Still redirect even if there's an error
    redirect('/')
  }
}

/**
 * Get user role from database (not JWT)
 * Following simplified role-based access control
 */
export async function getUserRole(): Promise<{
  role: AppRole;
  provider_role: ProviderRoleType | null;
} | null> {
  try {
    const supabase = await createClient()

    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return null
    }

    const { data, error } = await supabase
      .from('user_roles')
      .select('role, provider_role')
      .eq('user_id', user.id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        // No role found - return default
        return { role: 'user', provider_role: null }
      }
      console.error('Error fetching user role:', error.message)
      return null
    }

    return {
      role: data.role,
      provider_role: data.provider_role,
    }
  } catch (error) {
    console.error('Unexpected error in getUserRole:', error)
    return null
  }
}
/**
 * Role-based helper functions
 * Using simplified database-based role checking
 */
export async function isAdmin(): Promise<boolean> {
  const userRoleData = await getUserRole()
  return userRoleData?.role === 'admin'
}

export async function isProvider(): Promise<boolean> {
  const userRoleData = await getUserRole()
  return userRoleData?.role === 'catering_provider'
}

export async function isProviderOwner(): Promise<boolean> {
  const userRoleData = await getUserRole()
  return userRoleData?.role === 'catering_provider' && userRoleData?.provider_role === 'owner'
}

export async function isProviderStaff(): Promise<boolean> {
  const userRoleData = await getUserRole()
  return userRoleData?.role === 'catering_provider' && userRoleData?.provider_role === 'staff'
}

export async function debugJwtToken() {
  const supabase = await createClient()

  try {
    const { data: { session }, error } = await supabase.auth.getSession()

    if (error || !session) {
      console.log("❌ No session available:", error?.message || "Session is null")
      return null
    }

    // Decode and log JWT token details
    const token = session.access_token
    const payload = token.split('.')[1]

    if (!payload) {
      console.log("❌ Invalid JWT token format")
      return null
    }

    const decoded = JSON.parse(atob(payload)) as Record<string, unknown>

    console.log("🔍 JWT Token Debug Info:", {
      user_role: decoded.user_role,
      provider_role: decoded.provider_role,
      sub: decoded.sub,
      email: decoded.email,
      exp: decoded.exp ? new Date((decoded.exp as number) * 1000).toISOString() : 'unknown',
      iat: decoded.iat ? new Date((decoded.iat as number) * 1000).toISOString() : 'unknown',
      token_length: token.length,
      token_preview: token.substring(0, 50) + "..."
    })

    return decoded
  } catch (error) {
    console.error("❌ Error debugging JWT token:", error)
    return null
  }
}

export async function forceJwtRefresh(): Promise<boolean> {
  const supabase = await createClient()

  try {
    console.log("🔄 Forcing JWT refresh...")

    // First, let's check the current configuration
    console.log("🔧 Supabase Configuration Check:")
    console.log("- URL:", process.env.NEXT_PUBLIC_SUPABASE_URL?.substring(0, 30) + "...")
    console.log("- Anon Key:", process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.substring(0, 30) + "...")

    // Refresh the session to get a new JWT with updated claims
    const { error } = await supabase.auth.refreshSession()

    if (error) {
      console.error("❌ Failed to refresh session:", error)
      return false
    }

    console.log("✅ Session refresh completed")

    // Wait a moment for the new JWT to be available
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Verify the new JWT has the correct claims
    const newToken = await debugJwtToken()

    if (newToken && newToken.user_role === 'admin') {
      console.log("✅ JWT refresh successful - admin role confirmed")
      return true
    } else {
      console.warn("⚠️ JWT refresh completed but admin role not found")
      console.log("🔍 Current JWT claims:", newToken)
      return false
    }
  } catch (error) {
    console.error("❌ Error during JWT refresh:", error)
    return false
  }
}

export async function testSupabaseConnection(): Promise<boolean> {
  const supabase = await createClient()

  try {
    console.log("🧪 Testing Supabase connection...")

    // Test basic connection
    const { data, error } = await supabase.auth.getSession()

    if (error) {
      console.error("❌ Connection test failed:", error)
      return false
    }

    console.log("✅ Supabase connection successful")
    console.log("📊 Session data:", {
      hasSession: !!data.session,
      hasUser: !!data.session?.user,
      userEmail: data.session?.user?.email,
      tokenLength: data.session?.access_token?.length
    })

    return true
  } catch (error) {
    console.error("❌ Connection test error:", error)
    return false
  }
}

"use client";

import React from "react";
import { 
  useUser, 
  useProfile, 
  useUserRole, 
  useIsAdmin, 
  useIsProvider, 
  useIsProviderOwner,
  useSignOut,
  useAuthState 
} from "@/hooks/use-auth";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

/**
 * Authentication Test Component
 * This component tests all authentication hooks and displays the results
 * Used for validating the rebuilt authentication system
 */
export function AuthTest() {
  // Test individual hooks
  const { data: user, isLoading: userLoading, error: userError } = useUser();
  const { data: profile, isLoading: profileLoading, error: profileError } = useProfile();
  const { data: userRole, isLoading: roleLoading, error: roleError } = useUserRole();
  
  // Test convenience hooks
  const { isAdmin, isLoading: adminLoading } = useIsAdmin();
  const { isProvider, isLoading: providerLoading } = useIsProvider();
  const { isProviderOwner, isLoading: ownerLoading } = useIsProviderOwner();
  
  // Test composite hook
  const authState = useAuthState();
  
  // Test sign out mutation
  const signOutMutation = useSignOut();

  const handleSignOut = () => {
    signOutMutation.mutate();
  };

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle>Authentication System Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          
          {/* User Data */}
          <div>
            <h3 className="font-semibold mb-2">User Data</h3>
            <div className="text-sm space-y-1">
              <p><strong>Loading:</strong> {userLoading ? "Yes" : "No"}</p>
              <p><strong>Error:</strong> {userError ? userError.message : "None"}</p>
              <p><strong>User ID:</strong> {user?.id || "Not authenticated"}</p>
              <p><strong>Email:</strong> {user?.email || "N/A"}</p>
            </div>
          </div>

          <Separator />

          {/* Profile Data */}
          <div>
            <h3 className="font-semibold mb-2">Profile Data</h3>
            <div className="text-sm space-y-1">
              <p><strong>Loading:</strong> {profileLoading ? "Yes" : "No"}</p>
              <p><strong>Error:</strong> {profileError ? profileError.message : "None"}</p>
              <p><strong>Full Name:</strong> {profile?.full_name || "N/A"}</p>
              <p><strong>Avatar URL:</strong> {profile?.avatar_url || "N/A"}</p>
            </div>
          </div>

          <Separator />

          {/* Role Data */}
          <div>
            <h3 className="font-semibold mb-2">Role Data</h3>
            <div className="text-sm space-y-1">
              <p><strong>Loading:</strong> {roleLoading ? "Yes" : "No"}</p>
              <p><strong>Error:</strong> {roleError ? roleError.message : "None"}</p>
              <p><strong>Role:</strong> {userRole?.role || "N/A"}</p>
              <p><strong>Provider Role:</strong> {userRole?.provider_role || "N/A"}</p>
            </div>
          </div>

          <Separator />

          {/* Role Checks */}
          <div>
            <h3 className="font-semibold mb-2">Role Checks</h3>
            <div className="flex gap-2 flex-wrap">
              <Badge variant={isAdmin ? "default" : "secondary"}>
                Admin: {adminLoading ? "Loading..." : isAdmin ? "Yes" : "No"}
              </Badge>
              <Badge variant={isProvider ? "default" : "secondary"}>
                Provider: {providerLoading ? "Loading..." : isProvider ? "Yes" : "No"}
              </Badge>
              <Badge variant={isProviderOwner ? "default" : "secondary"}>
                Owner: {ownerLoading ? "Loading..." : isProviderOwner ? "Yes" : "No"}
              </Badge>
            </div>
          </div>

          <Separator />

          {/* Composite Auth State */}
          <div>
            <h3 className="font-semibold mb-2">Composite Auth State</h3>
            <div className="text-sm space-y-1">
              <p><strong>Is Loading:</strong> {authState.isLoading ? "Yes" : "No"}</p>
              <p><strong>Is Authenticated:</strong> {authState.isAuthenticated ? "Yes" : "No"}</p>
              <p><strong>Is Admin:</strong> {authState.isAdmin ? "Yes" : "No"}</p>
              <p><strong>Is Provider:</strong> {authState.isProvider ? "Yes" : "No"}</p>
              <p><strong>Is Provider Owner:</strong> {authState.isProviderOwner ? "Yes" : "No"}</p>
              <p><strong>Is Provider Staff:</strong> {authState.isProviderStaff ? "Yes" : "No"}</p>
            </div>
          </div>

          <Separator />

          {/* Actions */}
          <div>
            <h3 className="font-semibold mb-2">Actions</h3>
            <Button 
              onClick={handleSignOut}
              disabled={signOutMutation.isPending}
              variant="destructive"
            >
              {signOutMutation.isPending ? "Signing out..." : "Sign Out"}
            </Button>
          </div>

        </CardContent>
      </Card>
    </div>
  );
}

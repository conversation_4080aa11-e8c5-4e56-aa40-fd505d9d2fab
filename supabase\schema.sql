-- Create custom types for simplified roles
CREATE TYPE public.app_role AS ENUM ('user', 'admin', 'catering_provider');
CREATE TYPE public.provider_role_type AS ENUM ('owner', 'staff');

-- Create profiles table that extends the auth.users table
CREATE TABLE public.profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  full_name TEXT,
  avatar_url TEXT,
  updated_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create user_roles table to track user roles
CREATE TABLE public.user_roles (
  id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  role app_role NOT NULL,
  provider_role provider_role_type,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE (user_id, role)
);





-- Create simplified role checking functions
CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid()
    AND role = 'admin'
  );
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER
SET search_path = '';

CREATE OR REPLACE FUNCTION public.is_provider()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid()
    AND role = 'catering_provider'
  );
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER
SET search_path = '';

CREATE OR REPLACE FUNCTION public.is_provider_owner()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid()
    AND role = 'catering_provider'
    AND provider_role = 'owner'
  );
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER
SET search_path = '';

CREATE OR REPLACE FUNCTION public.get_user_role()
RETURNS public.app_role AS $$
DECLARE
  user_role public.app_role;
BEGIN
  SELECT role INTO user_role
  FROM public.user_roles
  WHERE user_id = auth.uid()
  LIMIT 1;

  RETURN COALESCE(user_role, 'user'::public.app_role);
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER
SET search_path = '';

-- Create function to set user role in JWT claims
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  avatar_url TEXT;
BEGIN
  -- Check for avatar_url in user metadata (from OAuth providers)
  -- For Google, the avatar URL is in the user's metadata
  IF NEW.raw_user_meta_data->>'avatar_url' IS NOT NULL THEN
    avatar_url := NEW.raw_user_meta_data->>'avatar_url';
  -- For Google specifically, it might also be in the identity provider data
  ELSIF NEW.identities IS NOT NULL AND jsonb_array_length(NEW.identities) > 0 THEN
    -- Check if the identity is from Google
    IF NEW.identities[0]->>'provider' = 'google' THEN
      -- Try to get the picture URL from identity data
      avatar_url := NEW.identities[0]->'identity_data'->>'picture';
    -- Check if the identity is from Facebook
    ELSIF NEW.identities[0]->>'provider' = 'facebook' THEN
      -- Try to get the picture URL from identity data
      avatar_url := NEW.identities[0]->'identity_data'->>'picture';
    END IF;
  ELSE
    avatar_url := NULL;
  END IF;

  -- Insert into public.profiles
  INSERT INTO public.profiles (id, full_name, avatar_url, updated_at)
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.identities[0]->'identity_data'->>'full_name', NEW.identities[0]->'identity_data'->>'name'),
    avatar_url,
    NOW()
  );

  -- Assign default 'user' role
  INSERT INTO public.user_roles (user_id, role)
  VALUES (NEW.id, 'user');

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to handle new user registration
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create custom access token hook to add role to JWT
CREATE OR REPLACE FUNCTION public.custom_access_token_hook(event JSONB)
RETURNS JSONB
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
SET search_path = ''
AS $$
DECLARE
  claims JSONB;
  user_role public.app_role;
  user_provider_role public.provider_role_type;
BEGIN
  -- Get user role information
  SELECT ur.role, ur.provider_role
  INTO user_role, user_provider_role
  FROM public.user_roles ur
  WHERE ur.user_id = (event->>'user_id')::uuid
  LIMIT 1;

  -- Set default role if none found
  user_role := COALESCE(user_role, 'user');

  -- Build simplified claims with only role information
  claims := jsonb_build_object(
    'user_role', user_role,
    'provider_role', user_provider_role
  );

  -- Return the event with updated claims
  RETURN jsonb_set(event, '{claims}', claims);
END;
$$;

-- Create performance indexes for simplified role-based queries
CREATE INDEX IF NOT EXISTS idx_user_roles_user_id_role ON public.user_roles (user_id, role);
CREATE INDEX IF NOT EXISTS idx_user_roles_role ON public.user_roles (role);
CREATE INDEX IF NOT EXISTS idx_user_roles_provider_role ON public.user_roles (provider_role) WHERE provider_role IS NOT NULL;

-- Set up RLS (Row Level Security) policies
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;

-- Simplified profiles policies using role-based checks
CREATE POLICY "profiles_select_policy"
  ON public.profiles FOR SELECT
  TO authenticated
  USING (
    -- Users can view their own profile
    id = (SELECT auth.uid())
    OR
    -- Admins can view all profiles
    EXISTS (
      SELECT 1 FROM public.user_roles
      WHERE user_id = (SELECT auth.uid())
      AND role = 'admin'
    )
  );

CREATE POLICY "profiles_update_policy"
  ON public.profiles FOR UPDATE
  TO authenticated
  USING (
    -- Users can update their own profile
    id = (SELECT auth.uid())
    OR
    -- Admins can update all profiles
    EXISTS (
      SELECT 1 FROM public.user_roles
      WHERE user_id = (SELECT auth.uid())
      AND role = 'admin'
    )
  );

CREATE POLICY "profiles_insert_policy"
  ON public.profiles FOR INSERT
  TO authenticated
  WITH CHECK (
    -- Users can insert their own profile
    id = (SELECT auth.uid())
  );

CREATE POLICY "profiles_delete_policy"
  ON public.profiles FOR DELETE
  TO authenticated
  USING (
    -- Only admins can delete profiles
    EXISTS (
      SELECT 1 FROM public.user_roles
      WHERE user_id = (SELECT auth.uid())
      AND role = 'admin'
    )
  );

-- Simplified user_roles policies using role-based checks
CREATE POLICY "user_roles_select_policy"
  ON public.user_roles FOR SELECT
  TO authenticated
  USING (
    -- Users can view their own roles
    user_id = (SELECT auth.uid())
    OR
    -- Admins can view all user roles
    EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.user_id = (SELECT auth.uid())
      AND ur.role = 'admin'
    )
  );

CREATE POLICY "user_roles_insert_policy"
  ON public.user_roles FOR INSERT
  TO authenticated
  WITH CHECK (
    -- Only admins can assign roles
    EXISTS (
      SELECT 1 FROM public.user_roles
      WHERE user_id = (SELECT auth.uid())
      AND role = 'admin'
    )
  );

CREATE POLICY "user_roles_update_policy"
  ON public.user_roles FOR UPDATE
  TO authenticated
  USING (
    -- Only admins can modify roles
    EXISTS (
      SELECT 1 FROM public.user_roles
      WHERE user_id = (SELECT auth.uid())
      AND role = 'admin'
    )
  )
  WITH CHECK (
    -- Only admins can modify roles
    EXISTS (
      SELECT 1 FROM public.user_roles
      WHERE user_id = (SELECT auth.uid())
      AND role = 'admin'
    )
  );

CREATE POLICY "user_roles_delete_policy"
  ON public.user_roles FOR DELETE
  TO authenticated
  USING (
    -- Only admins can delete roles
    EXISTS (
      SELECT 1 FROM public.user_roles
      WHERE user_id = (SELECT auth.uid())
      AND role = 'admin'
    )
  );

-- Special policy for auth hook to read user roles
CREATE POLICY "auth_hook_can_read_user_roles"
  ON public.user_roles FOR SELECT
  TO service_role
  USING (true);

-- Grant necessary permissions to supabase_auth_admin for simplified system
GRANT USAGE ON SCHEMA public TO supabase_auth_admin;
GRANT EXECUTE ON FUNCTION public.custom_access_token_hook TO supabase_auth_admin;
GRANT ALL ON TABLE public.user_roles TO supabase_auth_admin;
GRANT ALL ON TABLE public.profiles TO supabase_auth_admin;

-- Add comments for documentation
COMMENT ON FUNCTION public.is_admin() IS 'Returns true if the current user has admin role';
COMMENT ON FUNCTION public.is_provider() IS 'Returns true if the current user has catering_provider role';
COMMENT ON FUNCTION public.is_provider_owner() IS 'Returns true if the current user is a catering provider owner';
COMMENT ON FUNCTION public.get_user_role() IS 'Returns the current user''s role, defaulting to user if none found';
COMMENT ON FUNCTION public.custom_access_token_hook(jsonb) IS 'Simplified JWT hook that only includes role information in claims';

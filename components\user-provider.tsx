"use client";

import React, { ReactNode } from "react";
import { useAuthState } from "@/hooks/use-auth";
import { getAvatarUrl } from "@/lib/utils/avatar";

interface AuthProviderProps {
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * Clean authentication provider that works seamlessly in the background
 * Following official Supabase patterns without debug components
 * Integrates with TanStack Query for optimal caching and state management
 */
export function AuthProvider({ children, fallback }: AuthProviderProps) {
  const {
    user,
    profile,
    userRole,
    isLoading,
    isAuthenticated,
    isAdmin,
    isProvider,
    isProviderOwner,
    isProviderStaff,
    error,
  } = useAuthState();

  // Show loading fallback while authentication is being determined
  if (isLoading) {
    return (
      fallback || (
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      )
    );
  }

  // If there's an authentication error, fail silently and let the app handle it
  // This prevents the app from breaking due to temporary auth issues
  if (error) {
    // Log error for debugging but don't show error UI to users
    console.error("Authentication error:", error);
    // Return children anyway - individual components will handle auth requirements
    return <>{children}</>;
  }

  // Prepare clean user data for components
  const userData = user
    ? {
        id: user.id,
        name: profile?.full_name || user.email?.split("@")[0] || "User",
        email: user.email || "",
        avatar: getAvatarUrl(
          profile?.avatar_url,
          profile?.full_name || user.email?.split("@")[0] || "User"
        ),
        role: userRole?.role || "user",
        providerRole: userRole?.provider_role || null,
        isAdmin,
        isProvider,
        isProviderOwner,
        isProviderStaff,
      }
    : null;

  // Create auth context value
  const authContext = {
    user: userData,
    profile,
    userRole,
    isLoading,
    isAuthenticated,
    isAdmin,
    isProvider,
    isProviderOwner,
    isProviderStaff,
  };

  // Clone children with auth context
  return (
    <>
      {React.Children.map(children, (child) => {
        if (React.isValidElement(child)) {
          return React.cloneElement(
            child as React.ReactElement<{ auth?: typeof authContext }>,
            {
              auth: authContext,
            }
          );
        }
        return child;
      })}
    </>
  );
}

// Legacy alias for backward compatibility
export const UserProvider = AuthProvider;
